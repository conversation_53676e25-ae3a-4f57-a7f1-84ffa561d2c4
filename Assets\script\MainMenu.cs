using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.SceneManagement;
using UnityEngine.UI;

public class MainMenu : MonoBehaviour
{
    #region Level Management
    public GameObject[] LevelButton;
    public GameObject[] Level1;
    public GameObject[] level2;
    public static int levlno;
    #endregion

    #region Audio System
    public AudioSource BtnSound;
    public AudioSource Sound;
    public Slider music;
    public Slider sound;
    #endregion

    #region UI Elements
    public Text coins;
    public GameObject lodingpanl;
    #endregion
    #region Unity Lifecycle
    void Start()
    {
        InitializeGame();
        InitializeAudio();
        InitializeLevels();
    }
    #endregion

    #region Initialization Methods
    private void InitializeGame()
    {
        Time.timeScale = 1;
        coins.text = PlayerPrefs.GetInt("coins").ToString();
    }

    private void InitializeAudio()
    {
        // Initialize sliders with saved values
        music.value = PlayerPrefs.GetFloat("MusicVolume", 0.5f);
        sound.value = PlayerPrefs.GetFloat("SoundVolume", 0.5f);

        // Set initial volumes
        BtnSound.volume = music.value;
        Sound.volume = sound.value;

        // Add listeners for slider value changes
        music.onValueChanged.AddListener(OnMusicVolumeChanged);
        sound.onValueChanged.AddListener(OnSoundVolumeChanged);
    }

    private void InitializeLevels()
    {
        InitializeLevelButtons();
        InitializeLevel1();
        InitializeLevel2();
    }

    private void InitializeLevelButtons()
    {
        for (int i = 0; i < LevelButton.Length; i++)
        {
            if (PlayerPrefs.GetInt("Level" + i) == 1)
            {
                LevelButton[i + 1].transform.GetChild(0).gameObject.SetActive(false);
                LevelButton[i + 1].transform.GetComponent<Button>().interactable = true;
            }
        }
    }

    private void InitializeLevel1()
    {
        for (int i = 0; i < Level1.Length; i++)
        {
            if (PlayerPrefs.GetInt("Lvl" + i) == 1)
            {
                Level1[i + 1].transform.GetChild(0).gameObject.SetActive(false);
            }
        }
    }

    private void InitializeLevel2()
    {
        for (int i = 0; i < level2.Length; i++)
        {
            if (PlayerPrefs.GetInt("Lvll" + i) == 1)
            {
                level2[i + 1].transform.GetChild(0).gameObject.SetActive(false);
            }
        }
    }
    #endregion
    #region UI Navigation Methods
    public void yes()
    {
        Application.Quit();
    }

    #endregion
    #region Audio Settings Methods
    public void save()
    {

        // Save slider values to PlayerPrefs
        PlayerPrefs.SetFloat("MusicVolume", music.value);
        PlayerPrefs.SetFloat("SoundVolume", sound.value);
        PlayerPrefs.Save();
    }

    public void OnMusicVolumeChanged(float value)
    {
        BtnSound.volume = value;
        PlayerPrefs.SetFloat("MusicVolume", value);
    }

    public void OnSoundVolumeChanged(float value)
    {
        Sound.volume = value;
        PlayerPrefs.SetFloat("SoundVolume", value);
    }

    public void fill()
    {
        music.value = Mathf.Clamp01(music.value + 0.1f);
    }

    public void filldec()
    {
        music.value = Mathf.Clamp01(music.value - 0.1f);
    }

    public void fl()
    {
        sound.value = Mathf.Clamp01(sound.value + 0.1f);
    }

    public void fldec()
    {
        sound.value = Mathf.Clamp01(sound.value - 0.1f);
    }
    #endregion
    #region Level Selection Methods
    public void Careerlevel(int Clevel)
    {
        levlno = Clevel;
        lodingpanl.SetActive(true);
        StartCoroutine(GamePlayC());

    }

    public void Farmlevel(int Flevel)
    {
        levlno = Flevel;
        lodingpanl.SetActive(true);
        StartCoroutine(FarmingC());
       
    }

    public void Touchanlevel(int Tlevel)
    {
        levlno = Tlevel;
        lodingpanl.SetActive(true);
        StartCoroutine(TouchanC());
    }
    #endregion

    #region Control Settings Methods
    public void Steer()
    {
        RCC.SetMobileController(RCC_Settings.MobileController.SteeringWheel);
    }

    public void Btns()
    {
        RCC.SetMobileController(RCC_Settings.MobileController.TouchScreen);
    }

    public void Tilt()
    {
        RCC.SetMobileController(RCC_Settings.MobileController.Gyro);
    }
    #endregion
    #region Coroutines
    IEnumerator FarmingC()
    {
        yield return new WaitForSeconds(4f);
        SceneManager.LoadScene("Farming mod");
    }

    IEnumerator GamePlayC()
    {
        yield return new WaitForSeconds(1f);
        yield return new WaitForSeconds(3f);
        SceneManager.LoadScene("gameplay");
    }

    IEnumerator TouchanC()
    {
        yield return new WaitForSeconds(1f);
        yield return new WaitForSeconds(3f);
        SceneManager.LoadScene("tractortochan");
    }
    #endregion

    #region External Links Methods
    public void rateus()
    {
        Application.OpenURL("https://play.google.com/store/apps/details?id=com.smg.tractor.trolly.games.farming.game");
    }

    public void privacy()
    {
        Application.OpenURL("https://simulatorgames2022.blogspot.com/2023/04/privacy-policy.html");
    }

    public void moregams()
    {
        Application.OpenURL("https://play.google.com/store/apps/dev?id=6151632225219809775");
    }

    public void mudjeep()
    {
        Application.OpenURL("https://play.google.com/store/apps/details?id=com.smg.offroadjeep.mudjeep.offtheroad.jeepgame.simulator.offroad.driving.games");
    }

    public void trainadd()
    {
        Application.OpenURL("https://play.google.com/store/apps/details?id=com.smg.city.train.simulator.zt.game&hl=en");
    }
    #endregion
}
